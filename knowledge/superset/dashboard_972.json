{"normalized": {"dashboards": {"972": {"id": 972, "title": "Frontend Unavailability Reasoning", "url": "/superset/dashboard/972/", "description": "", "changed_on": "2025-09-18T09:58:25.415600", "owners": [21], "is_published": true, "chart_count": 58, "charts": [2242]}}, "charts": {"2242": {"id": 2242, "name": "Frontend Unavailability Reasoning Warehouse Level Overall", "viz_type": "table", "url": "/explore/?slice_id=2242", "dataset_id": 886, "dashboards": [972]}}, "datasets": {"886": {"id": 886, "schema": "viz", "table_name": "supply_etls_fe_unavailability_reasoning_warehouse_level_aggregated", "name": "viz.supply_etls_fe_unavailability_reasoning_warehouse_level_aggregated", "database": {"id": 21, "name": "blinkit-inventory-reporting-cluster", "backend": "trino"}}}}, "raw": {"pointers": {"dashboard": {"id": 972, "api_endpoint": "/api/v1/dashboard/972"}, "chart": {"id": 2242, "api_endpoint": "/api/v1/chart/2242"}, "dataset": {"id": 886, "api_endpoint": "/api/v1/dataset/886"}}, "dashboards": {"972": {"certification_details": null, "certified_by": null, "changed_by": {"first_name": "<PERSON><PERSON><PERSON>", "id": 21, "last_name": "<PERSON>"}, "changed_by_name": "<PERSON><PERSON><PERSON>", "changed_on": "2025-09-18T09:58:25.415600", "changed_on_delta_humanized": "a day ago", "dashboard_title": "Frontend Unavailability Reasoning", "id": 972, "is_managed_externally": false, "published": true, "slug": null, "thumbnail_url": "/api/v1/dashboard/972/thumbnail/55303538f8e617ea92dbebe6b3702119/", "url": "/superset/dashboard/972/"}}, "charts": {"2242": {"id": 2242, "slice_name": "Frontend Unavailability Reasoning Warehouse Level Overall", "viz_type": "table", "url": "/explore/?slice_id=2242", "thumbnail_url": "/api/v1/chart/2242/thumbnail/a42125c1418cd17438da3d1435c10adb/", "is_managed_externally": false, "dashboards": [{"id": 972, "dashboard_title": "Frontend Unavailability Reasoning"}], "datasource": {"id": "886", "type": "table", "uid": "886__table"}}}, "datasets": {"886": {"id": 886, "schema": "viz", "table_name": "supply_etls_fe_unavailability_reasoning_warehouse_level_aggregated", "datasource_type": "table", "name": "viz.supply_etls_fe_unavailability_reasoning_warehouse_level_aggregated", "uid": "886__table", "url": "/tablemodelview/edit/886", "database": {"id": 21, "database_name": "blinkit-inventory-reporting-cluster", "backend": "trino"}, "main_dttm_col": "observed_date", "kind": "physical", "is_sqllab_view": false, "is_managed_externally": false}}}, "notes": {"completeness": "This file contains a validated normalized section and a compact raw section with metadata pointers. Full, unmodified raw payloads (including large json_metadata and position_json) were omitted here to avoid editor truncation. They can be exported to separate files on request."}}