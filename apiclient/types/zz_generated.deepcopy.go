//go:build !ignore_autogenerated

// Code generated by controller-gen. DO NOT EDIT.

package types

import (
	"encoding/json"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *APIActivity) DeepCopyInto(out *APIActivity) {
	*out = *in
	in.Date.DeepCopyInto(&out.Date)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new APIActivity.
func (in *APIActivity) DeepCopy() *APIActivity {
	if in == nil {
		return nil
	}
	out := new(APIActivity)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *APIActivityList) DeepCopyInto(out *APIActivityList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]APIActivity, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new APIActivityList.
func (in *APIActivityList) DeepCopy() *APIActivityList {
	if in == nil {
		return nil
	}
	out := new(APIActivityList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessControlRule) DeepCopyInto(out *AccessControlRule) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.AccessControlRuleManifest.DeepCopyInto(&out.AccessControlRuleManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessControlRule.
func (in *AccessControlRule) DeepCopy() *AccessControlRule {
	if in == nil {
		return nil
	}
	out := new(AccessControlRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessControlRuleList) DeepCopyInto(out *AccessControlRuleList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]AccessControlRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessControlRuleList.
func (in *AccessControlRuleList) DeepCopy() *AccessControlRuleList {
	if in == nil {
		return nil
	}
	out := new(AccessControlRuleList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessControlRuleManifest) DeepCopyInto(out *AccessControlRuleManifest) {
	*out = *in
	if in.Subjects != nil {
		in, out := &in.Subjects, &out.Subjects
		*out = make([]Subject, len(*in))
		copy(*out, *in)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make([]Resource, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessControlRuleManifest.
func (in *AccessControlRuleManifest) DeepCopy() *AccessControlRuleManifest {
	if in == nil {
		return nil
	}
	out := new(AccessControlRuleManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Agent) DeepCopyInto(out *Agent) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.AgentManifest.DeepCopyInto(&out.AgentManifest)
	if in.AliasAssigned != nil {
		in, out := &in.AliasAssigned, &out.AliasAssigned
		*out = new(bool)
		**out = **in
	}
	if in.AuthStatus != nil {
		in, out := &in.AuthStatus, &out.AuthStatus
		*out = make(map[string]OAuthAppLoginAuthStatus, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	if in.ToolInfo != nil {
		in, out := &in.ToolInfo, &out.ToolInfo
		*out = new(map[string]ToolInfo)
		if **in != nil {
			in, out := *in, *out
			*out = make(map[string]ToolInfo, len(*in))
			for key, val := range *in {
				(*out)[key] = *val.DeepCopy()
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Agent.
func (in *Agent) DeepCopy() *Agent {
	if in == nil {
		return nil
	}
	out := new(Agent)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AgentIcons) DeepCopyInto(out *AgentIcons) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AgentIcons.
func (in *AgentIcons) DeepCopy() *AgentIcons {
	if in == nil {
		return nil
	}
	out := new(AgentIcons)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AgentList) DeepCopyInto(out *AgentList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Agent, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AgentList.
func (in *AgentList) DeepCopy() *AgentList {
	if in == nil {
		return nil
	}
	out := new(AgentList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AgentManifest) DeepCopyInto(out *AgentManifest) {
	*out = *in
	if in.Icons != nil {
		in, out := &in.Icons, &out.Icons
		*out = new(AgentIcons)
		**out = **in
	}
	if in.Temperature != nil {
		in, out := &in.Temperature, &out.Temperature
		*out = new(float32)
		**out = **in
	}
	if in.Cache != nil {
		in, out := &in.Cache, &out.Cache
		*out = new(bool)
		**out = **in
	}
	if in.Tools != nil {
		in, out := &in.Tools, &out.Tools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AvailableThreadTools != nil {
		in, out := &in.AvailableThreadTools, &out.AvailableThreadTools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.DefaultThreadTools != nil {
		in, out := &in.DefaultThreadTools, &out.DefaultThreadTools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.OAuthApps != nil {
		in, out := &in.OAuthApps, &out.OAuthApps
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.StarterMessages != nil {
		in, out := &in.StarterMessages, &out.StarterMessages
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = make([]EnvVar, len(*in))
		copy(*out, *in)
	}
	if in.Credentials != nil {
		in, out := &in.Credentials, &out.Credentials
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.WebsiteKnowledge != nil {
		in, out := &in.WebsiteKnowledge, &out.WebsiteKnowledge
		*out = new(WebsiteKnowledge)
		(*in).DeepCopyInto(*out)
	}
	if in.AllowedModelProviders != nil {
		in, out := &in.AllowedModelProviders, &out.AllowedModelProviders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowedModels != nil {
		in, out := &in.AllowedModels, &out.AllowedModels
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AgentManifest.
func (in *AgentManifest) DeepCopy() *AgentManifest {
	if in == nil {
		return nil
	}
	out := new(AgentManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Assistant) DeepCopyInto(out *Assistant) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.Icons = in.Icons
	if in.StarterMessages != nil {
		in, out := &in.StarterMessages, &out.StarterMessages
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.WebsiteKnowledge != nil {
		in, out := &in.WebsiteKnowledge, &out.WebsiteKnowledge
		*out = new(WebsiteKnowledge)
		(*in).DeepCopyInto(*out)
	}
	if in.AllowedModelProviders != nil {
		in, out := &in.AllowedModelProviders, &out.AllowedModelProviders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AvailableThreadTools != nil {
		in, out := &in.AvailableThreadTools, &out.AvailableThreadTools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.DefaultThreadTools != nil {
		in, out := &in.DefaultThreadTools, &out.DefaultThreadTools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Tools != nil {
		in, out := &in.Tools, &out.Tools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowedModels != nil {
		in, out := &in.AllowedModels, &out.AllowedModels
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Assistant.
func (in *Assistant) DeepCopy() *Assistant {
	if in == nil {
		return nil
	}
	out := new(Assistant)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AssistantList) DeepCopyInto(out *AssistantList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Assistant, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AssistantList.
func (in *AssistantList) DeepCopy() *AssistantList {
	if in == nil {
		return nil
	}
	out := new(AssistantList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AssistantTool) DeepCopyInto(out *AssistantTool) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.ToolManifest.DeepCopyInto(&out.ToolManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AssistantTool.
func (in *AssistantTool) DeepCopy() *AssistantTool {
	if in == nil {
		return nil
	}
	out := new(AssistantTool)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AssistantToolList) DeepCopyInto(out *AssistantToolList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]AssistantTool, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AssistantToolList.
func (in *AssistantToolList) DeepCopy() *AssistantToolList {
	if in == nil {
		return nil
	}
	out := new(AssistantToolList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthProvider) DeepCopyInto(out *AuthProvider) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.AuthProviderManifest = in.AuthProviderManifest
	in.AuthProviderStatus.DeepCopyInto(&out.AuthProviderStatus)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthProvider.
func (in *AuthProvider) DeepCopy() *AuthProvider {
	if in == nil {
		return nil
	}
	out := new(AuthProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthProviderList) DeepCopyInto(out *AuthProviderList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]AuthProvider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthProviderList.
func (in *AuthProviderList) DeepCopy() *AuthProviderList {
	if in == nil {
		return nil
	}
	out := new(AuthProviderList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthProviderManifest) DeepCopyInto(out *AuthProviderManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthProviderManifest.
func (in *AuthProviderManifest) DeepCopy() *AuthProviderManifest {
	if in == nil {
		return nil
	}
	out := new(AuthProviderManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthProviderStatus) DeepCopyInto(out *AuthProviderStatus) {
	*out = *in
	in.CommonProviderStatus.DeepCopyInto(&out.CommonProviderStatus)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthProviderStatus.
func (in *AuthProviderStatus) DeepCopy() *AuthProviderStatus {
	if in == nil {
		return nil
	}
	out := new(AuthProviderStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientInfo) DeepCopyInto(out *ClientInfo) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientInfo.
func (in *ClientInfo) DeepCopy() *ClientInfo {
	if in == nil {
		return nil
	}
	out := new(ClientInfo)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CommonProviderMetadata) DeepCopyInto(out *CommonProviderMetadata) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CommonProviderMetadata.
func (in *CommonProviderMetadata) DeepCopy() *CommonProviderMetadata {
	if in == nil {
		return nil
	}
	out := new(CommonProviderMetadata)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CommonProviderStatus) DeepCopyInto(out *CommonProviderStatus) {
	*out = *in
	out.CommonProviderMetadata = in.CommonProviderMetadata
	if in.RequiredConfigurationParameters != nil {
		in, out := &in.RequiredConfigurationParameters, &out.RequiredConfigurationParameters
		*out = make([]ProviderConfigurationParameter, len(*in))
		copy(*out, *in)
	}
	if in.OptionalConfigurationParameters != nil {
		in, out := &in.OptionalConfigurationParameters, &out.OptionalConfigurationParameters
		*out = make([]ProviderConfigurationParameter, len(*in))
		copy(*out, *in)
	}
	if in.MissingConfigurationParameters != nil {
		in, out := &in.MissingConfigurationParameters, &out.MissingConfigurationParameters
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CommonProviderStatus.
func (in *CommonProviderStatus) DeepCopy() *CommonProviderStatus {
	if in == nil {
		return nil
	}
	out := new(CommonProviderStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ContainerizedRuntimeConfig) DeepCopyInto(out *ContainerizedRuntimeConfig) {
	*out = *in
	if in.Args != nil {
		in, out := &in.Args, &out.Args
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ContainerizedRuntimeConfig.
func (in *ContainerizedRuntimeConfig) DeepCopy() *ContainerizedRuntimeConfig {
	if in == nil {
		return nil
	}
	out := new(ContainerizedRuntimeConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Credential) DeepCopyInto(out *Credential) {
	*out = *in
	if in.EnvVars != nil {
		in, out := &in.EnvVars, &out.EnvVars
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ExpiresAt != nil {
		in, out := &in.ExpiresAt, &out.ExpiresAt
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Credential.
func (in *Credential) DeepCopy() *Credential {
	if in == nil {
		return nil
	}
	out := new(Credential)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CredentialList) DeepCopyInto(out *CredentialList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Credential, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CredentialList.
func (in *CredentialList) DeepCopy() *CredentialList {
	if in == nil {
		return nil
	}
	out := new(CredentialList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CronJob) DeepCopyInto(out *CronJob) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.CronJobManifest.DeepCopyInto(&out.CronJobManifest)
	if in.LastRunStartedAt != nil {
		in, out := &in.LastRunStartedAt, &out.LastRunStartedAt
		*out = (*in).DeepCopy()
	}
	if in.LastSuccessfulRunCompleted != nil {
		in, out := &in.LastSuccessfulRunCompleted, &out.LastSuccessfulRunCompleted
		*out = (*in).DeepCopy()
	}
	if in.NextRunAt != nil {
		in, out := &in.NextRunAt, &out.NextRunAt
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CronJob.
func (in *CronJob) DeepCopy() *CronJob {
	if in == nil {
		return nil
	}
	out := new(CronJob)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CronJobList) DeepCopyInto(out *CronJobList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]CronJob, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CronJobList.
func (in *CronJobList) DeepCopy() *CronJobList {
	if in == nil {
		return nil
	}
	out := new(CronJobList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CronJobManifest) DeepCopyInto(out *CronJobManifest) {
	*out = *in
	if in.TaskSchedule != nil {
		in, out := &in.TaskSchedule, &out.TaskSchedule
		*out = new(Schedule)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CronJobManifest.
func (in *CronJobManifest) DeepCopy() *CronJobManifest {
	if in == nil {
		return nil
	}
	out := new(CronJobManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DefaultModelAlias) DeepCopyInto(out *DefaultModelAlias) {
	*out = *in
	out.DefaultModelAliasManifest = in.DefaultModelAliasManifest
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DefaultModelAlias.
func (in *DefaultModelAlias) DeepCopy() *DefaultModelAlias {
	if in == nil {
		return nil
	}
	out := new(DefaultModelAlias)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DefaultModelAliasList) DeepCopyInto(out *DefaultModelAliasList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]DefaultModelAlias, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DefaultModelAliasList.
func (in *DefaultModelAliasList) DeepCopy() *DefaultModelAliasList {
	if in == nil {
		return nil
	}
	out := new(DefaultModelAliasList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DefaultModelAliasManifest) DeepCopyInto(out *DefaultModelAliasManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DefaultModelAliasManifest.
func (in *DefaultModelAliasManifest) DeepCopy() *DefaultModelAliasManifest {
	if in == nil {
		return nil
	}
	out := new(DefaultModelAliasManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DeploymentCondition) DeepCopyInto(out *DeploymentCondition) {
	*out = *in
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	in.LastUpdateTime.DeepCopyInto(&out.LastUpdateTime)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DeploymentCondition.
func (in *DeploymentCondition) DeepCopy() *DeploymentCondition {
	if in == nil {
		return nil
	}
	out := new(DeploymentCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EmailReceiver) DeepCopyInto(out *EmailReceiver) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.EmailReceiverManifest.DeepCopyInto(&out.EmailReceiverManifest)
	if in.AliasAssigned != nil {
		in, out := &in.AliasAssigned, &out.AliasAssigned
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EmailReceiver.
func (in *EmailReceiver) DeepCopy() *EmailReceiver {
	if in == nil {
		return nil
	}
	out := new(EmailReceiver)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EmailReceiverList) DeepCopyInto(out *EmailReceiverList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]EmailReceiver, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EmailReceiverList.
func (in *EmailReceiverList) DeepCopy() *EmailReceiverList {
	if in == nil {
		return nil
	}
	out := new(EmailReceiverList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EmailReceiverManifest) DeepCopyInto(out *EmailReceiverManifest) {
	*out = *in
	if in.AllowedSenders != nil {
		in, out := &in.AllowedSenders, &out.AllowedSenders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EmailReceiverManifest.
func (in *EmailReceiverManifest) DeepCopy() *EmailReceiverManifest {
	if in == nil {
		return nil
	}
	out := new(EmailReceiverManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvVar) DeepCopyInto(out *EnvVar) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvVar.
func (in *EnvVar) DeepCopy() *EnvVar {
	if in == nil {
		return nil
	}
	out := new(EnvVar)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ErrHTTP) DeepCopyInto(out *ErrHTTP) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ErrHTTP.
func (in *ErrHTTP) DeepCopy() *ErrHTTP {
	if in == nil {
		return nil
	}
	out := new(ErrHTTP)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Field) DeepCopyInto(out *Field) {
	*out = *in
	if in.Sensitive != nil {
		in, out := &in.Sensitive, &out.Sensitive
		*out = new(bool)
		**out = **in
	}
	if in.Options != nil {
		in, out := &in.Options, &out.Options
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Field.
func (in *Field) DeepCopy() *Field {
	if in == nil {
		return nil
	}
	out := new(Field)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in Fields) DeepCopyInto(out *Fields) {
	{
		in := &in
		*out = make(Fields, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Fields.
func (in Fields) DeepCopy() Fields {
	if in == nil {
		return nil
	}
	out := new(Fields)
	in.DeepCopyInto(out)
	return *out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *File) DeepCopyInto(out *File) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new File.
func (in *File) DeepCopy() *File {
	if in == nil {
		return nil
	}
	out := new(File)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FileList) DeepCopyInto(out *FileList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]File, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FileList.
func (in *FileList) DeepCopy() *FileList {
	if in == nil {
		return nil
	}
	out := new(FileList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FileScannerProvider) DeepCopyInto(out *FileScannerProvider) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.FileScannerProviderManifest = in.FileScannerProviderManifest
	in.FileScannerProviderStatus.DeepCopyInto(&out.FileScannerProviderStatus)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FileScannerProvider.
func (in *FileScannerProvider) DeepCopy() *FileScannerProvider {
	if in == nil {
		return nil
	}
	out := new(FileScannerProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FileScannerProviderList) DeepCopyInto(out *FileScannerProviderList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]FileScannerProvider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FileScannerProviderList.
func (in *FileScannerProviderList) DeepCopy() *FileScannerProviderList {
	if in == nil {
		return nil
	}
	out := new(FileScannerProviderList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FileScannerProviderManifest) DeepCopyInto(out *FileScannerProviderManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FileScannerProviderManifest.
func (in *FileScannerProviderManifest) DeepCopy() *FileScannerProviderManifest {
	if in == nil {
		return nil
	}
	out := new(FileScannerProviderManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FileScannerProviderStatus) DeepCopyInto(out *FileScannerProviderStatus) {
	*out = *in
	in.CommonProviderStatus.DeepCopyInto(&out.CommonProviderStatus)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FileScannerProviderStatus.
func (in *FileScannerProviderStatus) DeepCopy() *FileScannerProviderStatus {
	if in == nil {
		return nil
	}
	out := new(FileScannerProviderStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in FolderSet) DeepCopyInto(out *FolderSet) {
	{
		in := &in
		*out = make(FolderSet, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FolderSet.
func (in FolderSet) DeepCopy() FolderSet {
	if in == nil {
		return nil
	}
	out := new(FolderSet)
	in.DeepCopyInto(out)
	return *out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Item) DeepCopyInto(out *Item) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Item.
func (in *Item) DeepCopy() *Item {
	if in == nil {
		return nil
	}
	out := new(Item)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KnowledgeFile) DeepCopyInto(out *KnowledgeFile) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	if in.Approved != nil {
		in, out := &in.Approved, &out.Approved
		*out = new(bool)
		**out = **in
	}
	if in.LastIngestionStartTime != nil {
		in, out := &in.LastIngestionStartTime, &out.LastIngestionStartTime
		*out = (*in).DeepCopy()
	}
	if in.LastIngestionEndTime != nil {
		in, out := &in.LastIngestionEndTime, &out.LastIngestionEndTime
		*out = (*in).DeepCopy()
	}
	if in.LastRunIDs != nil {
		in, out := &in.LastRunIDs, &out.LastRunIDs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KnowledgeFile.
func (in *KnowledgeFile) DeepCopy() *KnowledgeFile {
	if in == nil {
		return nil
	}
	out := new(KnowledgeFile)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KnowledgeFileList) DeepCopyInto(out *KnowledgeFileList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KnowledgeFile, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KnowledgeFileList.
func (in *KnowledgeFileList) DeepCopy() *KnowledgeFileList {
	if in == nil {
		return nil
	}
	out := new(KnowledgeFileList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KnowledgeSource) DeepCopyInto(out *KnowledgeSource) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.KnowledgeSourceManifest.DeepCopyInto(&out.KnowledgeSourceManifest)
	if in.SyncDetails != nil {
		in, out := &in.SyncDetails, &out.SyncDetails
		*out = make(json.RawMessage, len(*in))
		copy(*out, *in)
	}
	if in.LastSyncStartTime != nil {
		in, out := &in.LastSyncStartTime, &out.LastSyncStartTime
		*out = (*in).DeepCopy()
	}
	if in.LastSyncEndTime != nil {
		in, out := &in.LastSyncEndTime, &out.LastSyncEndTime
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KnowledgeSource.
func (in *KnowledgeSource) DeepCopy() *KnowledgeSource {
	if in == nil {
		return nil
	}
	out := new(KnowledgeSource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KnowledgeSourceInput) DeepCopyInto(out *KnowledgeSourceInput) {
	*out = *in
	if in.OneDriveConfig != nil {
		in, out := &in.OneDriveConfig, &out.OneDriveConfig
		*out = new(OneDriveConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.NotionConfig != nil {
		in, out := &in.NotionConfig, &out.NotionConfig
		*out = new(NotionConfig)
		**out = **in
	}
	if in.WebsiteCrawlingConfig != nil {
		in, out := &in.WebsiteCrawlingConfig, &out.WebsiteCrawlingConfig
		*out = new(WebsiteCrawlingConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KnowledgeSourceInput.
func (in *KnowledgeSourceInput) DeepCopy() *KnowledgeSourceInput {
	if in == nil {
		return nil
	}
	out := new(KnowledgeSourceInput)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KnowledgeSourceList) DeepCopyInto(out *KnowledgeSourceList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KnowledgeSource, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KnowledgeSourceList.
func (in *KnowledgeSourceList) DeepCopy() *KnowledgeSourceList {
	if in == nil {
		return nil
	}
	out := new(KnowledgeSourceList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KnowledgeSourceManifest) DeepCopyInto(out *KnowledgeSourceManifest) {
	*out = *in
	if in.AutoApprove != nil {
		in, out := &in.AutoApprove, &out.AutoApprove
		*out = new(bool)
		**out = **in
	}
	if in.FilePathPrefixInclude != nil {
		in, out := &in.FilePathPrefixInclude, &out.FilePathPrefixInclude
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.FilePathPrefixExclude != nil {
		in, out := &in.FilePathPrefixExclude, &out.FilePathPrefixExclude
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	in.KnowledgeSourceInput.DeepCopyInto(&out.KnowledgeSourceInput)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KnowledgeSourceManifest.
func (in *KnowledgeSourceManifest) DeepCopy() *KnowledgeSourceManifest {
	if in == nil {
		return nil
	}
	out := new(KnowledgeSourceManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPAuditLog) DeepCopyInto(out *MCPAuditLog) {
	*out = *in
	in.CreatedAt.DeepCopyInto(&out.CreatedAt)
	out.ClientInfo = in.ClientInfo
	if in.RequestBody != nil {
		in, out := &in.RequestBody, &out.RequestBody
		*out = make(json.RawMessage, len(*in))
		copy(*out, *in)
	}
	if in.ResponseBody != nil {
		in, out := &in.ResponseBody, &out.ResponseBody
		*out = make(json.RawMessage, len(*in))
		copy(*out, *in)
	}
	if in.WebhookStatuses != nil {
		in, out := &in.WebhookStatuses, &out.WebhookStatuses
		*out = make([]WebhookStatus, len(*in))
		copy(*out, *in)
	}
	if in.RequestHeaders != nil {
		in, out := &in.RequestHeaders, &out.RequestHeaders
		*out = make(json.RawMessage, len(*in))
		copy(*out, *in)
	}
	if in.ResponseHeaders != nil {
		in, out := &in.ResponseHeaders, &out.ResponseHeaders
		*out = make(json.RawMessage, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPAuditLog.
func (in *MCPAuditLog) DeepCopy() *MCPAuditLog {
	if in == nil {
		return nil
	}
	out := new(MCPAuditLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPAuditLogList) DeepCopyInto(out *MCPAuditLogList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPAuditLog, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPAuditLogList.
func (in *MCPAuditLogList) DeepCopy() *MCPAuditLogList {
	if in == nil {
		return nil
	}
	out := new(MCPAuditLogList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPAuditLogResponse) DeepCopyInto(out *MCPAuditLogResponse) {
	*out = *in
	in.MCPAuditLogList.DeepCopyInto(&out.MCPAuditLogList)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPAuditLogResponse.
func (in *MCPAuditLogResponse) DeepCopy() *MCPAuditLogResponse {
	if in == nil {
		return nil
	}
	out := new(MCPAuditLogResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPCatalog) DeepCopyInto(out *MCPCatalog) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.MCPCatalogManifest.DeepCopyInto(&out.MCPCatalogManifest)
	in.LastSynced.DeepCopyInto(&out.LastSynced)
	if in.SyncErrors != nil {
		in, out := &in.SyncErrors, &out.SyncErrors
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPCatalog.
func (in *MCPCatalog) DeepCopy() *MCPCatalog {
	if in == nil {
		return nil
	}
	out := new(MCPCatalog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPCatalogList) DeepCopyInto(out *MCPCatalogList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPCatalog, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPCatalogList.
func (in *MCPCatalogList) DeepCopy() *MCPCatalogList {
	if in == nil {
		return nil
	}
	out := new(MCPCatalogList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPCatalogManifest) DeepCopyInto(out *MCPCatalogManifest) {
	*out = *in
	if in.SourceURLs != nil {
		in, out := &in.SourceURLs, &out.SourceURLs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPCatalogManifest.
func (in *MCPCatalogManifest) DeepCopy() *MCPCatalogManifest {
	if in == nil {
		return nil
	}
	out := new(MCPCatalogManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPEnv) DeepCopyInto(out *MCPEnv) {
	*out = *in
	out.MCPHeader = in.MCPHeader
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPEnv.
func (in *MCPEnv) DeepCopy() *MCPEnv {
	if in == nil {
		return nil
	}
	out := new(MCPEnv)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPHeader) DeepCopyInto(out *MCPHeader) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPHeader.
func (in *MCPHeader) DeepCopy() *MCPHeader {
	if in == nil {
		return nil
	}
	out := new(MCPHeader)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPPromptReadStats) DeepCopyInto(out *MCPPromptReadStats) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPPromptReadStats.
func (in *MCPPromptReadStats) DeepCopy() *MCPPromptReadStats {
	if in == nil {
		return nil
	}
	out := new(MCPPromptReadStats)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPResourceReadStats) DeepCopyInto(out *MCPResourceReadStats) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPResourceReadStats.
func (in *MCPResourceReadStats) DeepCopy() *MCPResourceReadStats {
	if in == nil {
		return nil
	}
	out := new(MCPResourceReadStats)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPSelector) DeepCopyInto(out *MCPSelector) {
	*out = *in
	if in.Identifiers != nil {
		in, out := &in.Identifiers, &out.Identifiers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPSelector.
func (in *MCPSelector) DeepCopy() *MCPSelector {
	if in == nil {
		return nil
	}
	out := new(MCPSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in MCPSelectors) DeepCopyInto(out *MCPSelectors) {
	{
		in := &in
		*out = make(MCPSelectors, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPSelectors.
func (in MCPSelectors) DeepCopy() MCPSelectors {
	if in == nil {
		return nil
	}
	out := new(MCPSelectors)
	in.DeepCopyInto(out)
	return *out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServer) DeepCopyInto(out *MCPServer) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.MCPServerManifest.DeepCopyInto(&out.MCPServerManifest)
	if in.MissingRequiredEnvVars != nil {
		in, out := &in.MissingRequiredEnvVars, &out.MissingRequiredEnvVars
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MissingRequiredHeaders != nil {
		in, out := &in.MissingRequiredHeaders, &out.MissingRequiredHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MCPServerInstanceUserCount != nil {
		in, out := &in.MCPServerInstanceUserCount, &out.MCPServerInstanceUserCount
		*out = new(int)
		**out = **in
	}
	if in.DeploymentAvailableReplicas != nil {
		in, out := &in.DeploymentAvailableReplicas, &out.DeploymentAvailableReplicas
		*out = new(int32)
		**out = **in
	}
	if in.DeploymentReadyReplicas != nil {
		in, out := &in.DeploymentReadyReplicas, &out.DeploymentReadyReplicas
		*out = new(int32)
		**out = **in
	}
	if in.DeploymentReplicas != nil {
		in, out := &in.DeploymentReplicas, &out.DeploymentReplicas
		*out = new(int32)
		**out = **in
	}
	if in.DeploymentConditions != nil {
		in, out := &in.DeploymentConditions, &out.DeploymentConditions
		*out = make([]DeploymentCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServer.
func (in *MCPServer) DeepCopy() *MCPServer {
	if in == nil {
		return nil
	}
	out := new(MCPServer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerCatalogEntry) DeepCopyInto(out *MCPServerCatalogEntry) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.Manifest.DeepCopyInto(&out.Manifest)
	if in.LastUpdated != nil {
		in, out := &in.LastUpdated, &out.LastUpdated
		*out = (*in).DeepCopy()
	}
	if in.ToolPreviewsLastGenerated != nil {
		in, out := &in.ToolPreviewsLastGenerated, &out.ToolPreviewsLastGenerated
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerCatalogEntry.
func (in *MCPServerCatalogEntry) DeepCopy() *MCPServerCatalogEntry {
	if in == nil {
		return nil
	}
	out := new(MCPServerCatalogEntry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerCatalogEntryList) DeepCopyInto(out *MCPServerCatalogEntryList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPServerCatalogEntry, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerCatalogEntryList.
func (in *MCPServerCatalogEntryList) DeepCopy() *MCPServerCatalogEntryList {
	if in == nil {
		return nil
	}
	out := new(MCPServerCatalogEntryList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerCatalogEntryManifest) DeepCopyInto(out *MCPServerCatalogEntryManifest) {
	*out = *in
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.ToolPreview != nil {
		in, out := &in.ToolPreview, &out.ToolPreview
		*out = make([]MCPServerTool, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.UVXConfig != nil {
		in, out := &in.UVXConfig, &out.UVXConfig
		*out = new(UVXRuntimeConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.NPXConfig != nil {
		in, out := &in.NPXConfig, &out.NPXConfig
		*out = new(NPXRuntimeConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.ContainerizedConfig != nil {
		in, out := &in.ContainerizedConfig, &out.ContainerizedConfig
		*out = new(ContainerizedRuntimeConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.RemoteConfig != nil {
		in, out := &in.RemoteConfig, &out.RemoteConfig
		*out = new(RemoteCatalogConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = make([]MCPEnv, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerCatalogEntryManifest.
func (in *MCPServerCatalogEntryManifest) DeepCopy() *MCPServerCatalogEntryManifest {
	if in == nil {
		return nil
	}
	out := new(MCPServerCatalogEntryManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerDetails) DeepCopyInto(out *MCPServerDetails) {
	*out = *in
	in.LastRestart.DeepCopyInto(&out.LastRestart)
	if in.Events != nil {
		in, out := &in.Events, &out.Events
		*out = make([]MCPServerEvent, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerDetails.
func (in *MCPServerDetails) DeepCopy() *MCPServerDetails {
	if in == nil {
		return nil
	}
	out := new(MCPServerDetails)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerEvent) DeepCopyInto(out *MCPServerEvent) {
	*out = *in
	in.Time.DeepCopyInto(&out.Time)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerEvent.
func (in *MCPServerEvent) DeepCopy() *MCPServerEvent {
	if in == nil {
		return nil
	}
	out := new(MCPServerEvent)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerInstance) DeepCopyInto(out *MCPServerInstance) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerInstance.
func (in *MCPServerInstance) DeepCopy() *MCPServerInstance {
	if in == nil {
		return nil
	}
	out := new(MCPServerInstance)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerInstanceList) DeepCopyInto(out *MCPServerInstanceList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPServerInstance, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerInstanceList.
func (in *MCPServerInstanceList) DeepCopy() *MCPServerInstanceList {
	if in == nil {
		return nil
	}
	out := new(MCPServerInstanceList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerList) DeepCopyInto(out *MCPServerList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPServer, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerList.
func (in *MCPServerList) DeepCopy() *MCPServerList {
	if in == nil {
		return nil
	}
	out := new(MCPServerList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerManifest) DeepCopyInto(out *MCPServerManifest) {
	*out = *in
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.ToolPreview != nil {
		in, out := &in.ToolPreview, &out.ToolPreview
		*out = make([]MCPServerTool, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.UVXConfig != nil {
		in, out := &in.UVXConfig, &out.UVXConfig
		*out = new(UVXRuntimeConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.NPXConfig != nil {
		in, out := &in.NPXConfig, &out.NPXConfig
		*out = new(NPXRuntimeConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.ContainerizedConfig != nil {
		in, out := &in.ContainerizedConfig, &out.ContainerizedConfig
		*out = new(ContainerizedRuntimeConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.RemoteConfig != nil {
		in, out := &in.RemoteConfig, &out.RemoteConfig
		*out = new(RemoteRuntimeConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = make([]MCPEnv, len(*in))
		copy(*out, *in)
	}
	if in.Args != nil {
		in, out := &in.Args, &out.Args
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]MCPHeader, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerManifest.
func (in *MCPServerManifest) DeepCopy() *MCPServerManifest {
	if in == nil {
		return nil
	}
	out := new(MCPServerManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPServerTool) DeepCopyInto(out *MCPServerTool) {
	*out = *in
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Credentials != nil {
		in, out := &in.Credentials, &out.Credentials
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPServerTool.
func (in *MCPServerTool) DeepCopy() *MCPServerTool {
	if in == nil {
		return nil
	}
	out := new(MCPServerTool)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPToolCallStats) DeepCopyInto(out *MCPToolCallStats) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPToolCallStatsItem, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPToolCallStats.
func (in *MCPToolCallStats) DeepCopy() *MCPToolCallStats {
	if in == nil {
		return nil
	}
	out := new(MCPToolCallStats)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPToolCallStatsItem) DeepCopyInto(out *MCPToolCallStatsItem) {
	*out = *in
	in.CreatedAt.DeepCopyInto(&out.CreatedAt)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPToolCallStatsItem.
func (in *MCPToolCallStatsItem) DeepCopy() *MCPToolCallStatsItem {
	if in == nil {
		return nil
	}
	out := new(MCPToolCallStatsItem)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPUsageStatItem) DeepCopyInto(out *MCPUsageStatItem) {
	*out = *in
	if in.ToolCalls != nil {
		in, out := &in.ToolCalls, &out.ToolCalls
		*out = make([]MCPToolCallStats, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ResourceReads != nil {
		in, out := &in.ResourceReads, &out.ResourceReads
		*out = make([]MCPResourceReadStats, len(*in))
		copy(*out, *in)
	}
	if in.PromptReads != nil {
		in, out := &in.PromptReads, &out.PromptReads
		*out = make([]MCPPromptReadStats, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPUsageStatItem.
func (in *MCPUsageStatItem) DeepCopy() *MCPUsageStatItem {
	if in == nil {
		return nil
	}
	out := new(MCPUsageStatItem)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPUsageStats) DeepCopyInto(out *MCPUsageStats) {
	*out = *in
	in.TimeStart.DeepCopyInto(&out.TimeStart)
	in.TimeEnd.DeepCopyInto(&out.TimeEnd)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPUsageStatItem, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPUsageStats.
func (in *MCPUsageStats) DeepCopy() *MCPUsageStats {
	if in == nil {
		return nil
	}
	out := new(MCPUsageStats)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPUsageStatsList) DeepCopyInto(out *MCPUsageStatsList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPUsageStatItem, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPUsageStatsList.
func (in *MCPUsageStatsList) DeepCopy() *MCPUsageStatsList {
	if in == nil {
		return nil
	}
	out := new(MCPUsageStatsList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPWebhookValidation) DeepCopyInto(out *MCPWebhookValidation) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.MCPWebhookValidationManifest.DeepCopyInto(&out.MCPWebhookValidationManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPWebhookValidation.
func (in *MCPWebhookValidation) DeepCopy() *MCPWebhookValidation {
	if in == nil {
		return nil
	}
	out := new(MCPWebhookValidation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPWebhookValidationList) DeepCopyInto(out *MCPWebhookValidationList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]MCPWebhookValidation, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPWebhookValidationList.
func (in *MCPWebhookValidationList) DeepCopy() *MCPWebhookValidationList {
	if in == nil {
		return nil
	}
	out := new(MCPWebhookValidationList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MCPWebhookValidationManifest) DeepCopyInto(out *MCPWebhookValidationManifest) {
	*out = *in
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make([]Resource, len(*in))
		copy(*out, *in)
	}
	if in.Selectors != nil {
		in, out := &in.Selectors, &out.Selectors
		*out = make(MCPSelectors, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MCPWebhookValidationManifest.
func (in *MCPWebhookValidationManifest) DeepCopy() *MCPWebhookValidationManifest {
	if in == nil {
		return nil
	}
	out := new(MCPWebhookValidationManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Memory) DeepCopyInto(out *Memory) {
	*out = *in
	in.CreatedAt.DeepCopyInto(&out.CreatedAt)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Memory.
func (in *Memory) DeepCopy() *Memory {
	if in == nil {
		return nil
	}
	out := new(Memory)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MemoryList) DeepCopyInto(out *MemoryList) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Memory, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MemoryList.
func (in *MemoryList) DeepCopy() *MemoryList {
	if in == nil {
		return nil
	}
	out := new(MemoryList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Metadata) DeepCopyInto(out *Metadata) {
	*out = *in
	in.Created.DeepCopyInto(&out.Created)
	if in.Deleted != nil {
		in, out := &in.Deleted, &out.Deleted
		*out = (*in).DeepCopy()
	}
	if in.Links != nil {
		in, out := &in.Links, &out.Links
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Metadata.
func (in *Metadata) DeepCopy() *Metadata {
	if in == nil {
		return nil
	}
	out := new(Metadata)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Model) DeepCopyInto(out *Model) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.ModelManifest = in.ModelManifest
	in.ModelStatus.DeepCopyInto(&out.ModelStatus)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Model.
func (in *Model) DeepCopy() *Model {
	if in == nil {
		return nil
	}
	out := new(Model)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelList) DeepCopyInto(out *ModelList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Model, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelList.
func (in *ModelList) DeepCopy() *ModelList {
	if in == nil {
		return nil
	}
	out := new(ModelList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelManifest) DeepCopyInto(out *ModelManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelManifest.
func (in *ModelManifest) DeepCopy() *ModelManifest {
	if in == nil {
		return nil
	}
	out := new(ModelManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelProvider) DeepCopyInto(out *ModelProvider) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.ModelProviderManifest = in.ModelProviderManifest
	in.ModelProviderStatus.DeepCopyInto(&out.ModelProviderStatus)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelProvider.
func (in *ModelProvider) DeepCopy() *ModelProvider {
	if in == nil {
		return nil
	}
	out := new(ModelProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelProviderList) DeepCopyInto(out *ModelProviderList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ModelProvider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelProviderList.
func (in *ModelProviderList) DeepCopy() *ModelProviderList {
	if in == nil {
		return nil
	}
	out := new(ModelProviderList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelProviderManifest) DeepCopyInto(out *ModelProviderManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelProviderManifest.
func (in *ModelProviderManifest) DeepCopy() *ModelProviderManifest {
	if in == nil {
		return nil
	}
	out := new(ModelProviderManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelProviderStatus) DeepCopyInto(out *ModelProviderStatus) {
	*out = *in
	in.CommonProviderStatus.DeepCopyInto(&out.CommonProviderStatus)
	if in.ModelsBackPopulated != nil {
		in, out := &in.ModelsBackPopulated, &out.ModelsBackPopulated
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelProviderStatus.
func (in *ModelProviderStatus) DeepCopy() *ModelProviderStatus {
	if in == nil {
		return nil
	}
	out := new(ModelProviderStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ModelStatus) DeepCopyInto(out *ModelStatus) {
	*out = *in
	if in.AliasAssigned != nil {
		in, out := &in.AliasAssigned, &out.AliasAssigned
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ModelStatus.
func (in *ModelStatus) DeepCopy() *ModelStatus {
	if in == nil {
		return nil
	}
	out := new(ModelStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NPXRuntimeConfig) DeepCopyInto(out *NPXRuntimeConfig) {
	*out = *in
	if in.Args != nil {
		in, out := &in.Args, &out.Args
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NPXRuntimeConfig.
func (in *NPXRuntimeConfig) DeepCopy() *NPXRuntimeConfig {
	if in == nil {
		return nil
	}
	out := new(NPXRuntimeConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NotionConfig) DeepCopyInto(out *NotionConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NotionConfig.
func (in *NotionConfig) DeepCopy() *NotionConfig {
	if in == nil {
		return nil
	}
	out := new(NotionConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OAuthApp) DeepCopyInto(out *OAuthApp) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.OAuthAppManifest.DeepCopyInto(&out.OAuthAppManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OAuthApp.
func (in *OAuthApp) DeepCopy() *OAuthApp {
	if in == nil {
		return nil
	}
	out := new(OAuthApp)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OAuthAppList) DeepCopyInto(out *OAuthAppList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]OAuthApp, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OAuthAppList.
func (in *OAuthAppList) DeepCopy() *OAuthAppList {
	if in == nil {
		return nil
	}
	out := new(OAuthAppList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OAuthAppLoginAuthStatus) DeepCopyInto(out *OAuthAppLoginAuthStatus) {
	*out = *in
	if in.Required != nil {
		in, out := &in.Required, &out.Required
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OAuthAppLoginAuthStatus.
func (in *OAuthAppLoginAuthStatus) DeepCopy() *OAuthAppLoginAuthStatus {
	if in == nil {
		return nil
	}
	out := new(OAuthAppLoginAuthStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OAuthAppManifest) DeepCopyInto(out *OAuthAppManifest) {
	*out = *in
	if in.TenantID != nil {
		in, out := &in.TenantID, &out.TenantID
		*out = new(string)
		**out = **in
	}
	if in.Global != nil {
		in, out := &in.Global, &out.Global
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OAuthAppManifest.
func (in *OAuthAppManifest) DeepCopy() *OAuthAppManifest {
	if in == nil {
		return nil
	}
	out := new(OAuthAppManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OAuthClient) DeepCopyInto(out *OAuthClient) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.OAuthClientManifest.DeepCopyInto(&out.OAuthClientManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OAuthClient.
func (in *OAuthClient) DeepCopy() *OAuthClient {
	if in == nil {
		return nil
	}
	out := new(OAuthClient)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OAuthClientManifest) DeepCopyInto(out *OAuthClientManifest) {
	*out = *in
	if in.RedirectURIs != nil {
		in, out := &in.RedirectURIs, &out.RedirectURIs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.GrantTypes != nil {
		in, out := &in.GrantTypes, &out.GrantTypes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ResponseTypes != nil {
		in, out := &in.ResponseTypes, &out.ResponseTypes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Contacts != nil {
		in, out := &in.Contacts, &out.Contacts
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OAuthClientManifest.
func (in *OAuthClientManifest) DeepCopy() *OAuthClientManifest {
	if in == nil {
		return nil
	}
	out := new(OAuthClientManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OAuthToken) DeepCopyInto(out *OAuthToken) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OAuthToken.
func (in *OAuthToken) DeepCopy() *OAuthToken {
	if in == nil {
		return nil
	}
	out := new(OAuthToken)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OnEmail) DeepCopyInto(out *OnEmail) {
	*out = *in
	in.EmailReceiverManifest.DeepCopyInto(&out.EmailReceiverManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OnEmail.
func (in *OnEmail) DeepCopy() *OnEmail {
	if in == nil {
		return nil
	}
	out := new(OnEmail)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OnWebhook) DeepCopyInto(out *OnWebhook) {
	*out = *in
	in.WebhookManifest.DeepCopyInto(&out.WebhookManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OnWebhook.
func (in *OnWebhook) DeepCopy() *OnWebhook {
	if in == nil {
		return nil
	}
	out := new(OnWebhook)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OneDriveConfig) DeepCopyInto(out *OneDriveConfig) {
	*out = *in
	if in.SharedLinks != nil {
		in, out := &in.SharedLinks, &out.SharedLinks
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OneDriveConfig.
func (in *OneDriveConfig) DeepCopy() *OneDriveConfig {
	if in == nil {
		return nil
	}
	out := new(OneDriveConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PowerUserWorkspace) DeepCopyInto(out *PowerUserWorkspace) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PowerUserWorkspace.
func (in *PowerUserWorkspace) DeepCopy() *PowerUserWorkspace {
	if in == nil {
		return nil
	}
	out := new(PowerUserWorkspace)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PowerUserWorkspaceList) DeepCopyInto(out *PowerUserWorkspaceList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]PowerUserWorkspace, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PowerUserWorkspaceList.
func (in *PowerUserWorkspaceList) DeepCopy() *PowerUserWorkspaceList {
	if in == nil {
		return nil
	}
	out := new(PowerUserWorkspaceList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Progress) DeepCopyInto(out *Progress) {
	*out = *in
	if in.Time != nil {
		in, out := &in.Time, &out.Time
		*out = (*in).DeepCopy()
	}
	if in.StepTemplateInvoke != nil {
		in, out := &in.StepTemplateInvoke, &out.StepTemplateInvoke
		*out = new(StepTemplateInvoke)
		(*in).DeepCopyInto(*out)
	}
	if in.Prompt != nil {
		in, out := &in.Prompt, &out.Prompt
		*out = new(Prompt)
		(*in).DeepCopyInto(*out)
	}
	if in.Step != nil {
		in, out := &in.Step, &out.Step
		*out = new(Step)
		(*in).DeepCopyInto(*out)
	}
	if in.ToolInput != nil {
		in, out := &in.ToolInput, &out.ToolInput
		*out = new(ToolInput)
		(*in).DeepCopyInto(*out)
	}
	if in.ToolCall != nil {
		in, out := &in.ToolCall, &out.ToolCall
		*out = new(ToolCall)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Progress.
func (in *Progress) DeepCopy() *Progress {
	if in == nil {
		return nil
	}
	out := new(Progress)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Project) DeepCopyInto(out *Project) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.ProjectManifest.DeepCopyInto(&out.ProjectManifest)
	out.WorkflowNamesFromIntegration = in.WorkflowNamesFromIntegration
	if in.TemplateLastUpgraded != nil {
		in, out := &in.TemplateLastUpgraded, &out.TemplateLastUpgraded
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Project.
func (in *Project) DeepCopy() *Project {
	if in == nil {
		return nil
	}
	out := new(Project)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectCapabilities) DeepCopyInto(out *ProjectCapabilities) {
	*out = *in
	if in.OnEmail != nil {
		in, out := &in.OnEmail, &out.OnEmail
		*out = new(OnEmail)
		(*in).DeepCopyInto(*out)
	}
	if in.OnWebhook != nil {
		in, out := &in.OnWebhook, &out.OnWebhook
		*out = new(OnWebhook)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectCapabilities.
func (in *ProjectCapabilities) DeepCopy() *ProjectCapabilities {
	if in == nil {
		return nil
	}
	out := new(ProjectCapabilities)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectCredential) DeepCopyInto(out *ProjectCredential) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectCredential.
func (in *ProjectCredential) DeepCopy() *ProjectCredential {
	if in == nil {
		return nil
	}
	out := new(ProjectCredential)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectCredentialList) DeepCopyInto(out *ProjectCredentialList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ProjectCredential, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectCredentialList.
func (in *ProjectCredentialList) DeepCopy() *ProjectCredentialList {
	if in == nil {
		return nil
	}
	out := new(ProjectCredentialList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectInvitationManifest) DeepCopyInto(out *ProjectInvitationManifest) {
	*out = *in
	if in.Project != nil {
		in, out := &in.Project, &out.Project
		*out = new(Project)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectInvitationManifest.
func (in *ProjectInvitationManifest) DeepCopy() *ProjectInvitationManifest {
	if in == nil {
		return nil
	}
	out := new(ProjectInvitationManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectList) DeepCopyInto(out *ProjectList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Project, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectList.
func (in *ProjectList) DeepCopy() *ProjectList {
	if in == nil {
		return nil
	}
	out := new(ProjectList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectMCPServer) DeepCopyInto(out *ProjectMCPServer) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.ProjectMCPServerManifest = in.ProjectMCPServerManifest
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectMCPServer.
func (in *ProjectMCPServer) DeepCopy() *ProjectMCPServer {
	if in == nil {
		return nil
	}
	out := new(ProjectMCPServer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectMCPServerList) DeepCopyInto(out *ProjectMCPServerList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ProjectMCPServer, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectMCPServerList.
func (in *ProjectMCPServerList) DeepCopy() *ProjectMCPServerList {
	if in == nil {
		return nil
	}
	out := new(ProjectMCPServerList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectMCPServerManifest) DeepCopyInto(out *ProjectMCPServerManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectMCPServerManifest.
func (in *ProjectMCPServerManifest) DeepCopy() *ProjectMCPServerManifest {
	if in == nil {
		return nil
	}
	out := new(ProjectMCPServerManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectManifest) DeepCopyInto(out *ProjectManifest) {
	*out = *in
	in.ThreadManifest.DeepCopyInto(&out.ThreadManifest)
	if in.Capabilities != nil {
		in, out := &in.Capabilities, &out.Capabilities
		*out = new(ProjectCapabilities)
		(*in).DeepCopyInto(*out)
	}
	if in.Models != nil {
		in, out := &in.Models, &out.Models
		*out = make(map[string][]string, len(*in))
		for key, val := range *in {
			var outVal []string
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make([]string, len(*in))
				copy(*out, *in)
			}
			(*out)[key] = outVal
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectManifest.
func (in *ProjectManifest) DeepCopy() *ProjectManifest {
	if in == nil {
		return nil
	}
	out := new(ProjectManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectMember) DeepCopyInto(out *ProjectMember) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectMember.
func (in *ProjectMember) DeepCopy() *ProjectMember {
	if in == nil {
		return nil
	}
	out := new(ProjectMember)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectShare) DeepCopyInto(out *ProjectShare) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.ProjectShareManifest.DeepCopyInto(&out.ProjectShareManifest)
	if in.Icons != nil {
		in, out := &in.Icons, &out.Icons
		*out = new(AgentIcons)
		**out = **in
	}
	if in.Tools != nil {
		in, out := &in.Tools, &out.Tools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectShare.
func (in *ProjectShare) DeepCopy() *ProjectShare {
	if in == nil {
		return nil
	}
	out := new(ProjectShare)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectShareList) DeepCopyInto(out *ProjectShareList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ProjectShare, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectShareList.
func (in *ProjectShareList) DeepCopy() *ProjectShareList {
	if in == nil {
		return nil
	}
	out := new(ProjectShareList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectShareManifest) DeepCopyInto(out *ProjectShareManifest) {
	*out = *in
	if in.Users != nil {
		in, out := &in.Users, &out.Users
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectShareManifest.
func (in *ProjectShareManifest) DeepCopy() *ProjectShareManifest {
	if in == nil {
		return nil
	}
	out := new(ProjectShareManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectTemplate) DeepCopyInto(out *ProjectTemplate) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.ProjectSnapshot.DeepCopyInto(&out.ProjectSnapshot)
	if in.ProjectSnapshotLastUpgraded != nil {
		in, out := &in.ProjectSnapshotLastUpgraded, &out.ProjectSnapshotLastUpgraded
		*out = (*in).DeepCopy()
	}
	if in.MCPServers != nil {
		in, out := &in.MCPServers, &out.MCPServers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectTemplate.
func (in *ProjectTemplate) DeepCopy() *ProjectTemplate {
	if in == nil {
		return nil
	}
	out := new(ProjectTemplate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProjectTemplateList) DeepCopyInto(out *ProjectTemplateList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ProjectTemplate, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProjectTemplateList.
func (in *ProjectTemplateList) DeepCopy() *ProjectTemplateList {
	if in == nil {
		return nil
	}
	out := new(ProjectTemplateList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Prompt) DeepCopyInto(out *Prompt) {
	*out = *in
	if in.Time != nil {
		in, out := &in.Time, &out.Time
		*out = (*in).DeepCopy()
	}
	if in.Fields != nil {
		in, out := &in.Fields, &out.Fields
		*out = make(Fields, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Prompt.
func (in *Prompt) DeepCopy() *Prompt {
	if in == nil {
		return nil
	}
	out := new(Prompt)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PromptResponse) DeepCopyInto(out *PromptResponse) {
	*out = *in
	if in.Responses != nil {
		in, out := &in.Responses, &out.Responses
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PromptResponse.
func (in *PromptResponse) DeepCopy() *PromptResponse {
	if in == nil {
		return nil
	}
	out := new(PromptResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProviderConfigurationParameter) DeepCopyInto(out *ProviderConfigurationParameter) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProviderConfigurationParameter.
func (in *ProviderConfigurationParameter) DeepCopy() *ProviderConfigurationParameter {
	if in == nil {
		return nil
	}
	out := new(ProviderConfigurationParameter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RemainingTokenUsage) DeepCopyInto(out *RemainingTokenUsage) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RemainingTokenUsage.
func (in *RemainingTokenUsage) DeepCopy() *RemainingTokenUsage {
	if in == nil {
		return nil
	}
	out := new(RemainingTokenUsage)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RemainingTokenUsageList) DeepCopyInto(out *RemainingTokenUsageList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]RemainingTokenUsage, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RemainingTokenUsageList.
func (in *RemainingTokenUsageList) DeepCopy() *RemainingTokenUsageList {
	if in == nil {
		return nil
	}
	out := new(RemainingTokenUsageList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RemoteCatalogConfig) DeepCopyInto(out *RemoteCatalogConfig) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]MCPHeader, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RemoteCatalogConfig.
func (in *RemoteCatalogConfig) DeepCopy() *RemoteCatalogConfig {
	if in == nil {
		return nil
	}
	out := new(RemoteCatalogConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RemoteRuntimeConfig) DeepCopyInto(out *RemoteRuntimeConfig) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]MCPHeader, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RemoteRuntimeConfig.
func (in *RemoteRuntimeConfig) DeepCopy() *RemoteRuntimeConfig {
	if in == nil {
		return nil
	}
	out := new(RemoteRuntimeConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Resource) DeepCopyInto(out *Resource) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Resource.
func (in *Resource) DeepCopy() *Resource {
	if in == nil {
		return nil
	}
	out := new(Resource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Run) DeepCopyInto(out *Run) {
	*out = *in
	in.Created.DeepCopyInto(&out.Created)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Run.
func (in *Run) DeepCopy() *Run {
	if in == nil {
		return nil
	}
	out := new(Run)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RunList) DeepCopyInto(out *RunList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Run, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RunList.
func (in *RunList) DeepCopy() *RunList {
	if in == nil {
		return nil
	}
	out := new(RunList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RuntimeValidationError) DeepCopyInto(out *RuntimeValidationError) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RuntimeValidationError.
func (in *RuntimeValidationError) DeepCopy() *RuntimeValidationError {
	if in == nil {
		return nil
	}
	out := new(RuntimeValidationError)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Schedule) DeepCopyInto(out *Schedule) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Schedule.
func (in *Schedule) DeepCopy() *Schedule {
	if in == nil {
		return nil
	}
	out := new(Schedule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SlackReceiver) DeepCopyInto(out *SlackReceiver) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.SlackReceiverManifest = in.SlackReceiverManifest
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SlackReceiver.
func (in *SlackReceiver) DeepCopy() *SlackReceiver {
	if in == nil {
		return nil
	}
	out := new(SlackReceiver)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SlackReceiverList) DeepCopyInto(out *SlackReceiverList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]SlackReceiver, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SlackReceiverList.
func (in *SlackReceiverList) DeepCopy() *SlackReceiverList {
	if in == nil {
		return nil
	}
	out := new(SlackReceiverList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SlackReceiverManifest) DeepCopyInto(out *SlackReceiverManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SlackReceiverManifest.
func (in *SlackReceiverManifest) DeepCopy() *SlackReceiverManifest {
	if in == nil {
		return nil
	}
	out := new(SlackReceiverManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Step) DeepCopyInto(out *Step) {
	*out = *in
	if in.Loop != nil {
		in, out := &in.Loop, &out.Loop
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Step.
func (in *Step) DeepCopy() *Step {
	if in == nil {
		return nil
	}
	out := new(Step)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StepTemplateInvoke) DeepCopyInto(out *StepTemplateInvoke) {
	*out = *in
	if in.Args != nil {
		in, out := &in.Args, &out.Args
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StepTemplateInvoke.
func (in *StepTemplateInvoke) DeepCopy() *StepTemplateInvoke {
	if in == nil {
		return nil
	}
	out := new(StepTemplateInvoke)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Subject) DeepCopyInto(out *Subject) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Subject.
func (in *Subject) DeepCopy() *Subject {
	if in == nil {
		return nil
	}
	out := new(Subject)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Task) DeepCopyInto(out *Task) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.TaskManifest.DeepCopyInto(&out.TaskManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Task.
func (in *Task) DeepCopy() *Task {
	if in == nil {
		return nil
	}
	out := new(Task)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskEmail) DeepCopyInto(out *TaskEmail) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskEmail.
func (in *TaskEmail) DeepCopy() *TaskEmail {
	if in == nil {
		return nil
	}
	out := new(TaskEmail)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskList) DeepCopyInto(out *TaskList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Task, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskList.
func (in *TaskList) DeepCopy() *TaskList {
	if in == nil {
		return nil
	}
	out := new(TaskList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskManifest) DeepCopyInto(out *TaskManifest) {
	*out = *in
	if in.Steps != nil {
		in, out := &in.Steps, &out.Steps
		*out = make([]TaskStep, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Schedule != nil {
		in, out := &in.Schedule, &out.Schedule
		*out = new(Schedule)
		**out = **in
	}
	if in.Webhook != nil {
		in, out := &in.Webhook, &out.Webhook
		*out = new(TaskWebhook)
		**out = **in
	}
	if in.Email != nil {
		in, out := &in.Email, &out.Email
		*out = new(TaskEmail)
		**out = **in
	}
	if in.OnDemand != nil {
		in, out := &in.OnDemand, &out.OnDemand
		*out = new(TaskOnDemand)
		(*in).DeepCopyInto(*out)
	}
	if in.OnSlackMessage != nil {
		in, out := &in.OnSlackMessage, &out.OnSlackMessage
		*out = new(TaskOnSlackMessage)
		**out = **in
	}
	if in.OnDiscordMessage != nil {
		in, out := &in.OnDiscordMessage, &out.OnDiscordMessage
		*out = new(TaskOnDiscordMessage)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskManifest.
func (in *TaskManifest) DeepCopy() *TaskManifest {
	if in == nil {
		return nil
	}
	out := new(TaskManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskOnDemand) DeepCopyInto(out *TaskOnDemand) {
	*out = *in
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskOnDemand.
func (in *TaskOnDemand) DeepCopy() *TaskOnDemand {
	if in == nil {
		return nil
	}
	out := new(TaskOnDemand)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskOnDiscordMessage) DeepCopyInto(out *TaskOnDiscordMessage) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskOnDiscordMessage.
func (in *TaskOnDiscordMessage) DeepCopy() *TaskOnDiscordMessage {
	if in == nil {
		return nil
	}
	out := new(TaskOnDiscordMessage)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskOnSlackMessage) DeepCopyInto(out *TaskOnSlackMessage) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskOnSlackMessage.
func (in *TaskOnSlackMessage) DeepCopy() *TaskOnSlackMessage {
	if in == nil {
		return nil
	}
	out := new(TaskOnSlackMessage)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskRun) DeepCopyInto(out *TaskRun) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.Task.DeepCopyInto(&out.Task)
	if in.StartTime != nil {
		in, out := &in.StartTime, &out.StartTime
		*out = (*in).DeepCopy()
	}
	if in.EndTime != nil {
		in, out := &in.EndTime, &out.EndTime
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskRun.
func (in *TaskRun) DeepCopy() *TaskRun {
	if in == nil {
		return nil
	}
	out := new(TaskRun)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskRunList) DeepCopyInto(out *TaskRunList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]TaskRun, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskRunList.
func (in *TaskRunList) DeepCopy() *TaskRunList {
	if in == nil {
		return nil
	}
	out := new(TaskRunList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskStep) DeepCopyInto(out *TaskStep) {
	*out = *in
	if in.Loop != nil {
		in, out := &in.Loop, &out.Loop
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskStep.
func (in *TaskStep) DeepCopy() *TaskStep {
	if in == nil {
		return nil
	}
	out := new(TaskStep)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TaskWebhook) DeepCopyInto(out *TaskWebhook) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TaskWebhook.
func (in *TaskWebhook) DeepCopy() *TaskWebhook {
	if in == nil {
		return nil
	}
	out := new(TaskWebhook)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TemplateAuthorization) DeepCopyInto(out *TemplateAuthorization) {
	*out = *in
	out.TemplateAuthorizationManifest = in.TemplateAuthorizationManifest
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TemplateAuthorization.
func (in *TemplateAuthorization) DeepCopy() *TemplateAuthorization {
	if in == nil {
		return nil
	}
	out := new(TemplateAuthorization)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TemplateAuthorizationList) DeepCopyInto(out *TemplateAuthorizationList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]TemplateAuthorization, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TemplateAuthorizationList.
func (in *TemplateAuthorizationList) DeepCopy() *TemplateAuthorizationList {
	if in == nil {
		return nil
	}
	out := new(TemplateAuthorizationList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TemplateAuthorizationManifest) DeepCopyInto(out *TemplateAuthorizationManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TemplateAuthorizationManifest.
func (in *TemplateAuthorizationManifest) DeepCopy() *TemplateAuthorizationManifest {
	if in == nil {
		return nil
	}
	out := new(TemplateAuthorizationManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Thread) DeepCopyInto(out *Thread) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.ThreadManifest.DeepCopyInto(&out.ThreadManifest)
	if in.Env != nil {
		in, out := &in.Env, &out.Env
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Thread.
func (in *Thread) DeepCopy() *Thread {
	if in == nil {
		return nil
	}
	out := new(Thread)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ThreadAuthorization) DeepCopyInto(out *ThreadAuthorization) {
	*out = *in
	out.ThreadAuthorizationManifest = in.ThreadAuthorizationManifest
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ThreadAuthorization.
func (in *ThreadAuthorization) DeepCopy() *ThreadAuthorization {
	if in == nil {
		return nil
	}
	out := new(ThreadAuthorization)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ThreadAuthorizationList) DeepCopyInto(out *ThreadAuthorizationList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ThreadAuthorization, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ThreadAuthorizationList.
func (in *ThreadAuthorizationList) DeepCopy() *ThreadAuthorizationList {
	if in == nil {
		return nil
	}
	out := new(ThreadAuthorizationList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ThreadAuthorizationManifest) DeepCopyInto(out *ThreadAuthorizationManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ThreadAuthorizationManifest.
func (in *ThreadAuthorizationManifest) DeepCopy() *ThreadAuthorizationManifest {
	if in == nil {
		return nil
	}
	out := new(ThreadAuthorizationManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ThreadList) DeepCopyInto(out *ThreadList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Thread, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ThreadList.
func (in *ThreadList) DeepCopy() *ThreadList {
	if in == nil {
		return nil
	}
	out := new(ThreadList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ThreadManifest) DeepCopyInto(out *ThreadManifest) {
	*out = *in
	in.ThreadManifestManagedFields.DeepCopyInto(&out.ThreadManifestManagedFields)
	if in.Tools != nil {
		in, out := &in.Tools, &out.Tools
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.SharedTasks != nil {
		in, out := &in.SharedTasks, &out.SharedTasks
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowedMCPTools != nil {
		in, out := &in.AllowedMCPTools, &out.AllowedMCPTools
		*out = make(map[string][]string, len(*in))
		for key, val := range *in {
			var outVal []string
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make([]string, len(*in))
				copy(*out, *in)
			}
			(*out)[key] = outVal
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ThreadManifest.
func (in *ThreadManifest) DeepCopy() *ThreadManifest {
	if in == nil {
		return nil
	}
	out := new(ThreadManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ThreadManifestManagedFields) DeepCopyInto(out *ThreadManifestManagedFields) {
	*out = *in
	if in.Icons != nil {
		in, out := &in.Icons, &out.Icons
		*out = new(AgentIcons)
		**out = **in
	}
	if in.StarterMessages != nil {
		in, out := &in.StarterMessages, &out.StarterMessages
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.WebsiteKnowledge != nil {
		in, out := &in.WebsiteKnowledge, &out.WebsiteKnowledge
		*out = new(WebsiteKnowledge)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ThreadManifestManagedFields.
func (in *ThreadManifestManagedFields) DeepCopy() *ThreadManifestManagedFields {
	if in == nil {
		return nil
	}
	out := new(ThreadManifestManagedFields)
	in.DeepCopyInto(out)
	return out
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Time.
func (in *Time) DeepCopy() *Time {
	if in == nil {
		return nil
	}
	out := new(Time)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TokenUsage) DeepCopyInto(out *TokenUsage) {
	*out = *in
	in.Date.DeepCopyInto(&out.Date)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TokenUsage.
func (in *TokenUsage) DeepCopy() *TokenUsage {
	if in == nil {
		return nil
	}
	out := new(TokenUsage)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TokenUsageList) DeepCopyInto(out *TokenUsageList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]TokenUsage, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TokenUsageList.
func (in *TokenUsageList) DeepCopy() *TokenUsageList {
	if in == nil {
		return nil
	}
	out := new(TokenUsageList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolCall) DeepCopyInto(out *ToolCall) {
	*out = *in
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolCall.
func (in *ToolCall) DeepCopy() *ToolCall {
	if in == nil {
		return nil
	}
	out := new(ToolCall)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolInfo) DeepCopyInto(out *ToolInfo) {
	*out = *in
	if in.CredentialNames != nil {
		in, out := &in.CredentialNames, &out.CredentialNames
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolInfo.
func (in *ToolInfo) DeepCopy() *ToolInfo {
	if in == nil {
		return nil
	}
	out := new(ToolInfo)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolInput) DeepCopyInto(out *ToolInput) {
	*out = *in
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolInput.
func (in *ToolInput) DeepCopy() *ToolInput {
	if in == nil {
		return nil
	}
	out := new(ToolInput)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolManifest) DeepCopyInto(out *ToolManifest) {
	*out = *in
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolManifest.
func (in *ToolManifest) DeepCopy() *ToolManifest {
	if in == nil {
		return nil
	}
	out := new(ToolManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolReference) DeepCopyInto(out *ToolReference) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	out.ToolReferenceManifest = in.ToolReferenceManifest
	if in.Credentials != nil {
		in, out := &in.Credentials, &out.Credentials
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolReference.
func (in *ToolReference) DeepCopy() *ToolReference {
	if in == nil {
		return nil
	}
	out := new(ToolReference)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolReferenceList) DeepCopyInto(out *ToolReferenceList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ToolReference, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolReferenceList.
func (in *ToolReferenceList) DeepCopy() *ToolReferenceList {
	if in == nil {
		return nil
	}
	out := new(ToolReferenceList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ToolReferenceManifest) DeepCopyInto(out *ToolReferenceManifest) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ToolReferenceManifest.
func (in *ToolReferenceManifest) DeepCopy() *ToolReferenceManifest {
	if in == nil {
		return nil
	}
	out := new(ToolReferenceManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UVXRuntimeConfig) DeepCopyInto(out *UVXRuntimeConfig) {
	*out = *in
	if in.Args != nil {
		in, out := &in.Args, &out.Args
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UVXRuntimeConfig.
func (in *UVXRuntimeConfig) DeepCopy() *UVXRuntimeConfig {
	if in == nil {
		return nil
	}
	out := new(UVXRuntimeConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *User) DeepCopyInto(out *User) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.LastActiveDay.DeepCopyInto(&out.LastActiveDay)
	if in.DeletedAt != nil {
		in, out := &in.DeletedAt, &out.DeletedAt
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new User.
func (in *User) DeepCopy() *User {
	if in == nil {
		return nil
	}
	out := new(User)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UserDefaultRoleSetting) DeepCopyInto(out *UserDefaultRoleSetting) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UserDefaultRoleSetting.
func (in *UserDefaultRoleSetting) DeepCopy() *UserDefaultRoleSetting {
	if in == nil {
		return nil
	}
	out := new(UserDefaultRoleSetting)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UserList) DeepCopyInto(out *UserList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]User, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UserList.
func (in *UserList) DeepCopy() *UserList {
	if in == nil {
		return nil
	}
	out := new(UserList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Webhook) DeepCopyInto(out *Webhook) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.WebhookManifest.DeepCopyInto(&out.WebhookManifest)
	if in.AliasAssigned != nil {
		in, out := &in.AliasAssigned, &out.AliasAssigned
		*out = new(bool)
		**out = **in
	}
	if in.LastSuccessfulRunCompleted != nil {
		in, out := &in.LastSuccessfulRunCompleted, &out.LastSuccessfulRunCompleted
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Webhook.
func (in *Webhook) DeepCopy() *Webhook {
	if in == nil {
		return nil
	}
	out := new(Webhook)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WebhookList) DeepCopyInto(out *WebhookList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Webhook, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WebhookList.
func (in *WebhookList) DeepCopy() *WebhookList {
	if in == nil {
		return nil
	}
	out := new(WebhookList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WebhookManifest) DeepCopyInto(out *WebhookManifest) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WebhookManifest.
func (in *WebhookManifest) DeepCopy() *WebhookManifest {
	if in == nil {
		return nil
	}
	out := new(WebhookManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WebhookStatus) DeepCopyInto(out *WebhookStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WebhookStatus.
func (in *WebhookStatus) DeepCopy() *WebhookStatus {
	if in == nil {
		return nil
	}
	out := new(WebhookStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WebsiteCrawlingConfig) DeepCopyInto(out *WebsiteCrawlingConfig) {
	*out = *in
	if in.URLs != nil {
		in, out := &in.URLs, &out.URLs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WebsiteCrawlingConfig.
func (in *WebsiteCrawlingConfig) DeepCopy() *WebsiteCrawlingConfig {
	if in == nil {
		return nil
	}
	out := new(WebsiteCrawlingConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WebsiteDefinition) DeepCopyInto(out *WebsiteDefinition) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WebsiteDefinition.
func (in *WebsiteDefinition) DeepCopy() *WebsiteDefinition {
	if in == nil {
		return nil
	}
	out := new(WebsiteDefinition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WebsiteKnowledge) DeepCopyInto(out *WebsiteKnowledge) {
	*out = *in
	if in.Sites != nil {
		in, out := &in.Sites, &out.Sites
		*out = make([]WebsiteDefinition, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WebsiteKnowledge.
func (in *WebsiteKnowledge) DeepCopy() *WebsiteKnowledge {
	if in == nil {
		return nil
	}
	out := new(WebsiteKnowledge)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Workflow) DeepCopyInto(out *Workflow) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.WorkflowManifest.DeepCopyInto(&out.WorkflowManifest)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Workflow.
func (in *Workflow) DeepCopy() *Workflow {
	if in == nil {
		return nil
	}
	out := new(Workflow)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkflowExecution) DeepCopyInto(out *WorkflowExecution) {
	*out = *in
	in.Metadata.DeepCopyInto(&out.Metadata)
	in.Workflow.DeepCopyInto(&out.Workflow)
	in.StartTime.DeepCopyInto(&out.StartTime)
	if in.EndTime != nil {
		in, out := &in.EndTime, &out.EndTime
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkflowExecution.
func (in *WorkflowExecution) DeepCopy() *WorkflowExecution {
	if in == nil {
		return nil
	}
	out := new(WorkflowExecution)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkflowExecutionList) DeepCopyInto(out *WorkflowExecutionList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]WorkflowExecution, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkflowExecutionList.
func (in *WorkflowExecutionList) DeepCopy() *WorkflowExecutionList {
	if in == nil {
		return nil
	}
	out := new(WorkflowExecutionList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkflowList) DeepCopyInto(out *WorkflowList) {
	*out = *in
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Workflow, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkflowList.
func (in *WorkflowList) DeepCopy() *WorkflowList {
	if in == nil {
		return nil
	}
	out := new(WorkflowList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkflowManifest) DeepCopyInto(out *WorkflowManifest) {
	*out = *in
	if in.Steps != nil {
		in, out := &in.Steps, &out.Steps
		*out = make([]Step, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.OnSlackMessage != nil {
		in, out := &in.OnSlackMessage, &out.OnSlackMessage
		*out = new(TaskOnSlackMessage)
		**out = **in
	}
	if in.OnDiscordMessage != nil {
		in, out := &in.OnDiscordMessage, &out.OnDiscordMessage
		*out = new(TaskOnDiscordMessage)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkflowManifest.
func (in *WorkflowManifest) DeepCopy() *WorkflowManifest {
	if in == nil {
		return nil
	}
	out := new(WorkflowManifest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkflowNamesFromIntegration) DeepCopyInto(out *WorkflowNamesFromIntegration) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkflowNamesFromIntegration.
func (in *WorkflowNamesFromIntegration) DeepCopy() *WorkflowNamesFromIntegration {
	if in == nil {
		return nil
	}
	out := new(WorkflowNamesFromIntegration)
	in.DeepCopyInto(out)
	return out
}
