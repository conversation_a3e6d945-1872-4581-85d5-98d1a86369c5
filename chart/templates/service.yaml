apiVersion: v1
kind: Service
metadata:
  name: {{ include "obot.fullname" . }}
  labels:
    {{- include "obot.labels" . | nindent 4 }}
  {{- if .Values.service.annotations }}
  annotations:
    {{ .Values.service.annotations | toYaml | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    {{- include "obot.selectorLabels" . | nindent 4 }}
  {{- if .Values.service.spec }}
  {{ .Values.service.spec | toYaml | indent 2 }}
  {{- end }}