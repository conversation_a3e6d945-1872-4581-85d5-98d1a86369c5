name: Helm Chart

on:
  push:
    branches:
      - main
    paths:
      - chart/**
  pull_request:
    branches:
      - main
    paths:
      - chart/**

jobs:
  lint:
    runs-on: depot-ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up He<PERSON>
        uses: azure/setup-helm@v4

      - name: <PERSON><PERSON> <PERSON> chart
        run: helm lint chart