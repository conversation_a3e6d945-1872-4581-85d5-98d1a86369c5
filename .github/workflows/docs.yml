name: Documentation Build

on:
  pull_request:
    branches:
      - main
    paths:
      - "docs/**"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22"

      - name: Install dependencies
        run: npm install
        working-directory: docs

      - name: Build documentation
        run: npm run build
        working-directory: docs