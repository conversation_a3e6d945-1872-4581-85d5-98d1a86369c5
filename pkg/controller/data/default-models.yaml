items:
  - apiVersion: obot.obot.ai/v1
    kind: Model
    metadata:
      name: gpt-4.1
      namespace: default
    spec:
      manifest:
        targetModel: gpt-4.1
        modelProvider: openai-model-provider
        active: true
        usage: llm
  - apiVersion: obot.obot.ai/v1
    kind: Model
    metadata:
      name: text-embedding-ada-002
      namespace: default
    spec:
      manifest:
        targetModel: text-embedding-ada-002
        modelProvider: openai-model-provider
        active: true
        usage: text-embedding
  - apiVersion: obot.obot.ai/v1
    kind: Model
    metadata:
      name: text-embedding-3-small
      namespace: default
    spec:
      manifest:
        targetModel: text-embedding-3-small
        modelProvider: openai-model-provider
        active: true
        usage: text-embedding
  - apiVersion: obot.obot.ai/v1
    kind: Model
    metadata:
      name: text-embedding-3-large
      namespace: default
    spec:
      manifest:
        targetModel: text-embedding-3-large
        modelProvider: openai-model-provider
        active: true
        usage: text-embedding
  - apiVersion: obot.obot.ai/v1
    kind: Model
    metadata:
      name: dall-e-3
      namespace: default
    spec:
      manifest:
        targetModel: dall-e-3
        modelProvider: openai-model-provider
        active: true
        usage: image-generation
  - apiVersion: obot.obot.ai/v1
    kind: Model
    metadata:
      name: gpt-4.1-mini
      namespace: default
    spec:
      manifest:
        targetModel: gpt-4.1-mini
        modelProvider: openai-model-provider
        active: true
        usage: llm
  - apiVersion: obot.obot.ai/v1
    kind: Model
    metadata:
      name: gpt-3.5-turbo
      namespace: default
    spec:
      manifest:
        targetModel: gpt-3.5-turbo
        modelProvider: openai-model-provider
        active: true
        usage: llm
