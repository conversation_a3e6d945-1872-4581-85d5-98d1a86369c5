name: go

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    paths-ignore:
      - ui/admin/**
      - ui/user/**

jobs:
  lint:
    runs-on: depot-ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'

      - name: Build
        run: make build

      - name: Validate Go Code
        run: make validate-go-code
