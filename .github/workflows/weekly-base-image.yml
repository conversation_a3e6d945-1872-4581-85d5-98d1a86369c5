name: Weekly Base Image

permissions:
  id-token: write
  contents: read
  packages: write

on:
  workflow_dispatch:
  schedule:
    - cron: '0 21 * * 5'

jobs:
  build:
    runs-on: depot-ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Depot
        uses: depot/setup-action@v1

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }}
          password: ${{ secrets.GHCR_TOKEN }}

      - name: Build and push base Docker image
        uses: depot/build-push-action@v1
        id: build-and-push
        with:
          project: bbqjs4tj1g
          context: .
          push: true
          pull: true
          target: base
          platforms: linux/amd64,linux/arm64
          tags: |
            ghcr.io/${{ github.repository }}/base:latest

      - name: Install Cosign
        uses: sigstore/cosign-installer@v3.8.1
        with:
          cosign-release: 'v2.4.3'
      - name: Check install!
        run: cosign version

      - name: Sign Images
        env:
          DIGEST: ${{ steps.build-and-push.outputs.digest }}
          TAGS: ghcr.io/${{ github.repository }}/base:latest
        run: |
          images=""
          for tag in ${TAGS}; do
            images+="${tag}@${DIGEST} "
          done
          cosign sign --yes ${images}
