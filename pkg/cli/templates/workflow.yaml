# Required to indicate that this is an workflow
type: workflow

# A display name for your Workflow
name: "%NAME%"

# Description of your Workflow
description: ""

# The instructions for the Workflow
prompt: |
  You are a friendly AI Assistant

# The tools available to the workflow always. These tools can not be removed by a user
tools: []

# Workflow steps
steps:
  - step: Pick a random number
    tools: []
  - if:
      condition: The number is equal to 6
      steps:
        - step: Say you win
      else:
        - step: Say you lose

# A floating point number between 0 and 1 (ex: 0.7) that will control how creative the Agent is
# 0 means no creativity, whereas 0.7 is a good balance between creativity and relevance
temperature: null

# Set cache to false to disable caching of any AI responses
cache: null

# A description of the knowledge that this workflow has access to. This description helps the LLM know when
# to query knowledge
knowledgeDescription: ""
