# Required to indicate that this is an agent
type: agent

# A display name for your Agent
name: "%NAME%"

# Description of your Agent
description: ""

# The instructions for the Agent
prompt: |
  You are a friendly AI Assistant

# The tools available to the agent always. These tools can not be removed by a user
tools: []

# List of tools that will be available to the user to add to their conversation
availableThreadTools: []

# List of tool that will be enabled by default for new user conversation, but can be removed
defaultThreadTools: []

# A floating point number between 0 and 1 (ex: 0.7) that will control how creative the Agent is
# 0 means no creativity, whereas 0.7 is a good balance between creativity and relevance
temperature: null

# Set cache to false to disable caching of any AI responses
cache: null

# Icons for display
icons:
  collapsed: ""
  collapsedDark: ""
  icon: ""
  iconDark: ""
  
# A description of the knowledge that this agent has access to. This description helps the LLM know when
# to query knowledge
knowledgeDescription: ""