{{- if not .Values.config.existingSecret -}}
{{- $secretName := ( include "obot.config.secretName" . ) }}
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  labels:
    {{- include "obot.labels" . | nindent 4 }}
  name: {{ include "obot.config.secretName" . }}
data:
  {{- range $key, $value := .Values.config }}
  {{- if (toString $value) }}
  {{ $key }}: {{ toString $value | b64enc }}
  {{- end }}
  {{- end }}
  {{- if eq .Values.config.OBOT_SERVER_MCPRUNTIME_BACKEND "kubernetes" }}
  OBOT_SERVER_MCPNAMESPACE: {{ include "obot.config.mcpNamespace" . | b64enc }}
  {{- if gt (len .Values.mcpImagePullSecrets) 0 }}
  OBOT_SERVER_MCPIMAGE_PULL_SECRETS: {{ include "obot.config.mcpImagePullSecrets" . | b64enc }}
  {{- end }}
  {{- end }}
  {{- if eq .Values.config.OBOT_BOOTSTRAP_TOKEN "" }}
    {{- $existing := (lookup "v1" "Secret" .Release.Namespace $secretName) }}
    {{- if $existing }}
      {{- $val := index $existing.data "OBOT_BOOTSTRAP_TOKEN" }}
  OBOT_BOOTSTRAP_TOKEN: {{ $val }}
    {{- else }}
      {{- $random := randAlphaNum 32 }}
  OBOT_BOOTSTRAP_TOKEN: {{ $random | b64enc }}
    {{- end }}
  {{- end }}
{{- end -}}
