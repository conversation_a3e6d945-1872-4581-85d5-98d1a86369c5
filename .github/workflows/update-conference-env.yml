name: Update Conference Env

permissions:
  id-token: write
  contents: read
  packages: write

on:
  workflow_dispatch:

jobs:
  copy-tag:
    runs-on: depot-ubuntu-22.04

    steps:
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup crane
        uses: imjasonh/setup-crane@v0.4

      - name: Copy to chat tag
        run: |
          crane tag ghcr.io/${{ github.repository }}-enterprise:main conference
