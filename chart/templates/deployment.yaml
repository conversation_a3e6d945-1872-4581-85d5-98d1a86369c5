apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "obot.fullname" . }}
  labels:
    {{- include "obot.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: {{ .Values.updateStrategy }}
  selector:
    matchLabels:
      {{- include "obot.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- if not .Values.config.existingSecret }}
      annotations:
        checksum/config-secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      {{- end }}
      labels:
        {{- include "obot.selectorLabels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ include "obot.serviceAccountName" . }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml .Values.imagePullSecrets | nindent 8 }}
      {{- end }}
      {{- if eq .Values.config.OBOT_SERVER_ENCRYPTION_PROVIDER "custom" }}
      initContainers:
        - name: encryptionsetup
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: OBOT_SERVER_ENCRYPTION_KEY
              valueFrom:
                secretKeyRef:
                  key: OBOT_SERVER_ENCRYPTION_KEY
                  name: {{ include "obot.config.secretName" . }}
          command:
          - /bin/sh
          - -c
          - |
            cat > /config/encryption.yaml <<EOF
            kind: EncryptionConfiguration
            apiVersion: apiserver.config.k8s.io/v1
            resources:
              - resources:
                  - credentials
                  - runstates.obot.obot.ai
                  - users.obot.obot.ai
                  - identities.obot.obot.ai
                  - mcpoauthtokens.obot.obot.ai
                providers:
                  - aesgcm:
                      keys:
                        - name: key0
                          secret: "$(OBOT_SERVER_ENCRYPTION_KEY)"
                  - identity: {}
            EOF
          volumeMounts:
            - name: config
              mountPath: /config
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            {{- if eq .Values.config.OBOT_SERVER_ENCRYPTION_PROVIDER "custom" }}
            - name: config
              mountPath: /config
            {{- end }}
            {{- if .Values.persistence.enabled }}
            - name: data
              mountPath: {{ .Values.persistence.path }}
            {{- end }}
            {{- if .Values.extraVolumeMounts }}
            {{- toYaml .Values.extraVolumeMounts | nindent 12 }}
            {{- end }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/healthz
              port: http
            failureThreshold: 5
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /api/healthz
              port: http
            failureThreshold: 5
            periodSeconds: 10
          {{- if .Values.extraEnv }}
          env:
            {{- range $key, $value := .Values.extraEnv }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- end }}
          envFrom:
            - secretRef:
                name: {{ if .Values.config.existingSecret }}{{ .Values.config.existingSecret }}{{ else }}{{ include "obot.config.secretName" . }}{{- end }}
          {{- with .Values.extraEnvFrom }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
      {{- if eq .Values.config.OBOT_SERVER_ENCRYPTION_PROVIDER "custom" }}
        - name: config
          emptyDir:
            medium: Memory
      {{- end }}
      {{- if .Values.persistence.enabled }}
        - name: data
          persistentVolumeClaim:
            claimName: {{ ternary .Values.persistence.existingClaim (print .Release.Name "-pvc") (ne .Values.persistence.existingClaim "") }}
      {{- end }}
      {{- if .Values.extraVolumes }}
        {{- if .Values.extraVolumes }}
        {{- toYaml .Values.extraVolumes | nindent 8 }}
        {{- end }}
      {{- end }}
