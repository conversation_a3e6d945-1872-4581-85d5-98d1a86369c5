---
title: Access Control
---

## Overview

Obot allows the admin to select what MCP Servers users will have access to - either by individual user or group. Using this mechanism you could allow different users to have access to different MCP Servers.

To configure this, go to the Obot Admin and select `Access Control` from the left sidebar. There you will be able to see and edit existing rules, as well as add new ones.

## Default Access

By default, there's an "everyone" group that's assigned to all users initially. This means anyone that logs into the Obot Gateway will have access to all of the MCP servers in the main catalog.

If this default behavior is not what you want, you'll want to restrict access down to specific users or remove the "everyone" group entirely. However, it's recommended that administrators at least should have access to all servers.

## Creating Access Control Rules

To create new access control rules:

1. Click the **Add New Rule** button in the Access Control section
2. Give your access control group a new name
3. Assign users and groups to the access control group
4. Add the MCP servers that this group should have access to

## Example: Marketing Team Access

For instance, if you were creating access controls for a marketing team:

1. Create a new access control rule named "Marketing Team"
2. Assign your marketing team members, either individually or through an existing group
3. Add relevant MCP servers such as:
   - Email tools
   - Google Calendar
   - Google Sheets
   - CRM systems
   - Other tools your marketing team needs for their day-to-day work

This approach ensures that each team only has access to the tools they need while maintaining security and organization.
