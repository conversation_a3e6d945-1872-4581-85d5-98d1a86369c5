name: Update Chat Env

permissions:
  id-token: write
  contents: read
  packages: write

on:
  schedule:
    - cron: "0 03 * * *"
  workflow_dispatch:

jobs:
  copy-tag:
    if: github.event_name != 'schedule' || vars.DISABLE_CHAT_AUTO_UPDATE != 'true'
    runs-on: depot-ubuntu-22.04

    steps:
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup crane
        uses: imjasonh/setup-crane@v0.4

      - name: Copy to chat tag
        run: |
          crane tag ghcr.io/${{ github.repository }}-enterprise:main chat
