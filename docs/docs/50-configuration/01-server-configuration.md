# Server Configuration

The Obot server is configured via environment variables. The following configuration is available:

| Environment Variable | Description |
|---------------------|-------------|
| `OPENAI_API_KEY` | The foundation of Obot is a large language model that supports function-calling. The default is OpenAI and specifying an OpenAI key here will ensure none of the users need to worry about specifying their own API key. |
| `ANTHROPIC_API_KEY` | You can also provide an Anthropic API key in place of or in addition to an OpenAI API key. |
| `GITHUB_AUTH_TOKEN` | Obot and its underlying tool GPTScript make heavy use of tools hosted on GitHub. Care is taken to cache these tools and only re-check when necessary. However, rate-limiting can happen. Setting a read-only token here can alleviate many of these issues. |
| `OBOT_SERVER_DSN` | Obot uses a database backend. By default, it will use a sqlite3 local database. This environment variable allows you to specify another database option. For example, you can use a postgres database with something like `OBOT_SERVER_DSN=**************************************`. |
| `OBOT_SERVER_HOSTNAME` | Tell Obot what its server URL is so that things like <PERSON>A<PERSON>, LLM proxying, and invoke URLs are handled correctly. |
| `OBOT_SERVER_RETENTION_POLICY_HOURS` | The retention policy for the system. Set to 0 to disable retention. Default is 2160 (90 days) if left unset. This field should just be a number in a string, no `h` suffix. |
| `NAH_THREADINESS` | Sets the number of concurrent threads that can run in the Obot controller. |
| `OBOT_SERVER_KNOWLEDGE_FILE_WORKERS` | Sets the number of workers used by knowledge for processing files. |
| `KINM_DB_CONNECTIONS` | The number of connections in the database pool for kinm |
| `GITHUB_AUTH_TOKEN` | A github PAT, used to authenticate tool pulls to avoid github ratelimiting |
| `OBOT_SERVER_ENABLE_AUTHENTICATION` | Enables authentication for Obot |
| `OBOT_SERVER_ENCRYPTION_PROVIDER` | Configures an encryption provider for credentials in Obot. One of "aws", "gcp", "azure", or "custom" |
| `OBOT_SERVER_ENCRYPTION_CONFIG_FILE` | The path to a file containing the encryption configuration. |
| `OBOT_SERVER_ENCRYPTION_KEY` | Sets the key to be used for encryption. Should only be set if `OBOT_SERVER_ENCRYPTION_PROVIDER` is `custom` |
| `OBOT_BOOTSTRAP_TOKEN` | Sets a bootstrap token. If authentication is enabled, one will be autogenerated for you if this is not set. |
| `OBOT_SERVER_AUTH_ADMIN_EMAILS` | A comma separated list of email addresses that will have the Admin role in Obot. |
| `OBOT_SERVER_OTEL_BASE_EXPORT_ENDPOINT` | The base export endpoint for OpenTelemetry |
| `OBOT_SERVER_OTEL_SAMPLE_PROB` | The sampling probability for OpenTelemetry |
| `OBOT_SERVER_OTEL_BEARER_TOKEN` | The bearer token for authentication with OpenTelemetry |
| `OBOT_SERVER_AUDIT_LOGS_MODE` | Configures the storage backend for audit logs in Obot. Can be 'off', 'disk', or 's3' |
| `OBOT_SERVER_AUDIT_LOGS_STORE_S3BUCKET` | The name of the S3 bucket to store audit logs in. |
| `OBOT_SERVER_AUDIT_LOGS_STORE_S3ENDPOINT` | If config.OBOT_SERVER_AUDIT_LOGS_MODE is 's3' and you are not using AWS S3, this needs to be set to the S3 api endpoint of your provider. |
| `OBOT_SERVER_AUDIT_LOGS_COMPRESS_FILE` | Controls whether or not to compress audit log files |
| `OBOT_SERVER_AUDIT_LOGS_USE_PATH_STYLE` | Whether to use path style for S3 |
| `OBOT_SERVER_MCPBASE_IMAGE` | Deploy MCP servers in the kubernetes cluster or using docker with this base image. |
| `OBOT_SERVER_MCPRUNTIME_BACKEND` | The runtime backend to use for running MCP servers: docker, kubernetes, or local. Defaults to docker. |
| `OBOT_SERVER_MCPCLUSTER_DOMAIN` | The cluster domain to use for MCP services. Defaults to cluster.local. Only matters if `OBOT_SERVER_MCPBASE_IMAGE` is set. |
`OBOT_SERVER_DISALLOW_LOCALHOST_MCP` | disallow MCP servers that try to connect to localhost. Defaults to false. |

## Encryption Providers

You can configure optional [encryption providers](99-encryption-providers/01-aws-kms.md) to ensure secrets are encrypted.

## Model Providers
You can configure additional models using [model providers](04-model-providers.md)

## Workspace Provider
You can configure external storage for workspace files using a [workspace provider](05-workspace-provider.md)
