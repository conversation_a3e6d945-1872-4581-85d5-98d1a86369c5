name: Test Docker Build
on:
  pull_request:
    branches:
      - main
    paths-ignore:
      - docs/**
      - chart/**

jobs:
  build:
    runs-on: depot-ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Depot
        uses: depot/setup-action@v1

      - name: Build Docker Image
        uses: depot/build-push-action@v1
        with:
          project: bbqjs4tj1g
          context: .
          platforms: linux/amd64,linux/arm64
          build-args: |
            BASE_IMAGE=ghcr.io/obot-platform/obot/base:latest
