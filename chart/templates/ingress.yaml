{{- if .Values.ingress.enabled -}}
{{- $fullName := include "obot.fullname" . -}}
{{- $ingressPaths := .Values.ingress.paths -}}
{{- $extraPaths := .Values.ingress.extraPaths -}}
apiVersion: "networking.k8s.io/v1"
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "obot.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className }}
{{- end }}
{{- if .Values.ingress.tls }}
  tls:
  {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ . | quote }}
      http:
        paths:
        {{- if $extraPaths }}
{{ $extraPaths | toYaml | indent 10 }}
        {{- end }}
        {{- range $ingressPaths }}
          - path: {{ or .path . | quote }}
            pathType: {{ .pathType | quote }}
            backend:
              service:
                name: {{ $fullName }}
                port:
                  name: http
        {{- end }}
  {{- end }}
{{- end }}