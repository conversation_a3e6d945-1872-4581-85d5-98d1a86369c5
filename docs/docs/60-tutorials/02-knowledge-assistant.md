# Create a HR Q&A Project

Obot makes it easy to create a project that can answer questions about a set of documents using its built-in RAG (Retrieval-Augmented Generation) feature.
This guide walks you through creating an HR Q&A project step by step.

:::note
As you configure the project, changes will be saved and applied automatically.
:::

## 1. Setting up the project
Start by going to the Obot homepage. Click on your profile picture in the top right and chose **Chat** from the dropdown.
If you do not have an existing project, one will automatically be created for you. If you do already have a project, you can click on the **+** in the left sidebar next to the name of the project you are currently in.
Set the project name and description to whatever you would like in the fields on the left hand side.

Next, name your project and optionally write a description of what the project should be used for.

Next, click the gear nex to the project name in the sidebar and write some instructions for the project.
This is a prompt that explains what you would like the project to do for you.
Here is one example you can try:

```text
You are an HR assistant that answers employee questions based on official company HR policies and procedures. Always be clear, concise, and refer to the relevant policy where possible.
```

## 2. Adding Documents

To provide the project with knowledge, click on the gear icon at the top of the sidebar. You can click the `Upload` button under the **Knowledge Files** header to add files.

## Testing the project

Once you've added your documents, you can test the project in the chat interface.

Assuming you added relevant documents as knowledge, ask sample questions like:

```text
What is the company's remote work policy?

How many sick days are provided annually?
```

This helps ensure the project is answering correctly based on the documents provided.